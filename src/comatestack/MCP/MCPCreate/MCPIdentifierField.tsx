/* eslint-disable max-lines */
import styled from '@emotion/styled';
import {Path} from '@panda-design/path-form';
import {Button, message} from '@panda-design/components';
import {Flex, Form, Tooltip, Upload} from 'antd';
import {useBoolean} from 'huse';
import {IconUpload} from '@baidu/ee-icon';
import {useEffect} from 'react';
import {Gap} from '@/design/iplayground/Gap';
import {apiPostLogo} from '@/api/mcp';

export const MCPLogoWrapper = styled.div`
    display: inline-block;
    height: 85px;
    width: 85px;
    border-radius: 6px;
    border: 1px solid #D9D9D9;
    background: #E4E4E4;
    overflow: hidden;
    text-align: center;
    img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }
`;

const StyledButton = styled(Button)`
    color: #317ff5 !important;
    padding: 0 12px !important;
    border: 1px solid #317ff5 !important;
    &:hover{
        color: #0080FF !important;
        border: 1px solid #0080FF !important;
    }
`;

const ImageListWrapper = styled.div`
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
`;

const TooltipContentWrapper = styled.div`
    padding: 6px;
`;

const UploadTitle = styled.span`
    display: inline-block;
    font-size: 14px;
    font-weight: 500;
    line-height: 22px;
    margin: 8px 0;
`;

const GrayText = styled.span`
    color: #8F8F8F;
    font-size: 14px;
    line-height: 22px;
`;

const defaultIcons = [
    'http://bj.bcebos.com/agentic-infra-msg-test/mcp-plugin-center/logo/49b4c428-dc6b-4f87-a61f-9c77955ab84b_HapiGo_2025-06-17_17.52.29.png',
    'http://bj.bcebos.com/agentic-infra-msg-test/mcp-plugin-center/logo/bf4eeb0d-e414-4f00-8f61-07b7842857b1_HapiGo_2025-06-17_17.52.20.png',
    'http://bj.bcebos.com/agentic-infra-msg-test/mcp-plugin-center/logo/756ad577-c418-40c2-8c5d-9f34b32d7724_HapiGo_2025-06-17_17.52.46.png',
    'http://bj.bcebos.com/agentic-infra-msg-test/mcp-plugin-center/logo/49c0f29e-c195-4db5-9cbf-6762534f9520_HapiGo_2025-06-17_17.52.36.png',
];

interface Props {
    value?: string;
    onChange?: (value: string) => void;
}

const IdentifierChooser = ({onChange}: Props) => {
    const [loading, {on, off}] = useBoolean();
    const handleUpload = async (file: File) => {
        on();
        try {
            const overSize = file.size / 1024 > 500;
            if (overSize) {
                message.error('请选择小于500KB的图片文件');
                off();
            }
            const formData = new FormData();
            formData.append('file', file);
            const result = await apiPostLogo(formData);
            onChange?.(result.fileLink);
            message.success('上传成功');
            off();
        } catch (error) {
            message.error('上传失败');
            off();
        }
    };

    const beforeUpload = (file: File) => {
        handleUpload(file);
        return false; // 阻止默认上传行为
    };
    return (
        <TooltipContentWrapper onClick={e => e.stopPropagation()}>
            <UploadTitle>选择一个标识</UploadTitle>
            <ImageListWrapper>
                {defaultIcons.map((url, index) => (
                    <MCPLogoWrapper
                        style={{cursor: 'pointer'}}
                        onClick={
                            () => {
                                onChange?.(url);
                            }
                        }
                        key={index}
                    >
                        <img alt="logo" src={url} />
                    </MCPLogoWrapper>
                ))}
            </ImageListWrapper>
            <Gap />
            <Flex justify="space-between" align="center">
                <Flex vertical>
                    <UploadTitle>上传自定义标识</UploadTitle>
                    <GrayText>支持JPG 或 PNG，图片最大 500 KB</GrayText>
                </Flex>
                <Upload
                    beforeUpload={beforeUpload}
                    showUploadList={false}
                    accept=".jpg,.png"
                >
                    <StyledButton icon={<IconUpload />} loading={loading}>上传</StyledButton>
                </Upload>
            </Flex>

        </TooltipContentWrapper>
    );
};

const IdentifierField = ({value, onChange}: Props) => {
    const [open, {on, off}] = useBoolean();
    useEffect(
        () => {
            const offTooltip = () => {
                off();
            };
            window.addEventListener('click', offTooltip);
            return () => {
                window.removeEventListener('click', offTooltip);
            };
        },
        [off]
    );
    return (
        <Flex vertical style={{width: 85}} gap={8}>
            <MCPLogoWrapper>
                {value ? (
                    <img src={value} alt="logo" />
                ) : (
                    <img src={defaultIcons[0]} alt="logo" />
                )}
            </MCPLogoWrapper>
            <Tooltip
                open={open}
                trigger="click"
                placement="bottomLeft"
                title={(
                    <IdentifierChooser
                        value={value}
                        onChange={value => {
                            onChange?.(value);
                            off();
                        }}
                    />
                )}
            >
                <StyledButton
                    onClick={e => {
                        e.stopPropagation();
                        on();
                    }}
                >
                    修改标识
                </StyledButton>
            </Tooltip>
        </Flex>
    );
};

interface Props {
    path?: Path;
}

const MCPIdentifierField = ({path = []}: Props) => {
    return (
        <Form.Item
            name={[...path, 'icon']}
            label="MCP标识"
            rules={[{required: true, message: '请输入MCP标识'}]}
            initialValue={defaultIcons[0]}
        >
            <IdentifierField />
        </Form.Item>
    );
};

export default MCPIdentifierField;
